---
title: Teleg<PERSON> Userbot - Auto Reply When Inactive
emoji: 🤖
colorFrom: blue
colorTo: purple
sdk: docker
pinned: false
license: mit
---

# 🤖 Telegram Userbot - Auto Reply When Inactive

بوت تيليجرام شخصي يقوم بالرد التلقائي على الرسائل الخاصة فقط عندما تكون غير نشط.

## ✨ المميزات

- 🎯 **رد تلقائي ذكي**: يرد فقط عندما تكون غير نشط لفترة محددة
- 🔒 **آمن وخاص**: يعمل على حسابك الشخصي بدون مشاركة البيانات
- ⚡ **خفيف وسريع**: استهلاك قليل للموارد
- 🛡️ **حماية من التكرار**: لا يكرر الرد لنفس الشخص
- ⚙️ **قابل للتخصيص**: إعدادات مرنة وسهلة التعديل
- 📝 **سجلات مفصلة**: تتبع نشاط البوت (اختياري)
- 🐳 **دعم Docker**: تشغيل سهل ومستمر مع حفظ الجلسة

## 🚀 التثبيت والإعداد

### طريقة 1: استخدام Docker (الموصى بها)

#### 1. متطلبات النظام
```bash
Docker
Docker Compose
```

#### 2. إعداد البوت
```bash
# استنساخ المشروع
git clone <repository-url>
cd telegram-userbot

# إنشاء مجلد البيانات
mkdir -p data

# تعديل ملف الإعدادات
nano config.json
```

#### 3. تشغيل البوت
```bash
# بناء وتشغيل البوت
docker-compose up -d

# مراقبة السجلات
docker-compose logs -f

# إيقاف البوت
docker-compose down
```

### طريقة 2: التثبيت المحلي

#### 1. متطلبات النظام
```bash
Python 3.8+
```

#### 2. تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

### 3. الحصول على API Credentials

1. اذهب إلى [my.telegram.org](https://my.telegram.org)
2. سجل دخولك برقم هاتفك
3. اذهب إلى "API Development Tools"
4. أنشئ تطبيق جديد واحصل على:
   - `api_id`
   - `api_hash`

### 4. تكوين البوت

عدّل ملف `config.json`:

```json
{
    "api_id": "1234567",
    "api_hash": "abcdef1234567890abcdef1234567890",
    "phone_number": "+1234567890",
    "inactivity_minutes": 5,
    "auto_reply_message": "مرحباً! أنا غير متاح حالياً، سأرد عليك في أقرب وقت ممكن. 🙏",
    "excluded_users": [],
    "enable_logging": true,
    "session_name": "userbot_session",
    "rate_limit_seconds": 60,
    "debug_mode": false
}
```

### 5. تشغيل البوت

```bash
python main.py
```

في المرة الأولى، ستحتاج إلى:
- إدخال رمز التحقق المرسل إلى تيليجرام
- إدخال كلمة مرور الحساب (إذا كانت مفعلة)

## 🌐 نشر على Hugging Face Spaces

### 1. إنشاء Space جديد
- اذهب إلى [Hugging Face Spaces](https://huggingface.co/spaces)
- انقر على "Create new Space"
- اختر "Docker" كـ SDK
- ارفع جميع الملفات

### 2. إعداد المتغيرات السرية
في إعدادات Space، أضف المتغيرات التالية كـ Secrets:
- `API_ID`: معرف API الخاص بك
- `API_HASH`: مفتاح API الخاص بك  
- `PHONE_NUMBER`: رقم هاتفك

### 3. تعديل config.json للـ Spaces
```json
{
    "api_id": "$API_ID",
    "api_hash": "$API_HASH", 
    "phone_number": "$PHONE_NUMBER",
    "inactivity_minutes": 5,
    "auto_reply_message": "مرحباً! أنا غير موجود حالياً، سأرد عليك في أقرب وقت ممكن. 🙏",
    "excluded_users": [],
    "enable_logging": true,
    "session_name": "userbot_session",
    "rate_limit_seconds": 60,
    "debug_mode": false
}
```

### 4. الجلسة الأولى
- في المرة الأولى، ستحتاج لتسجيل الدخول عبر logs الـ Space
- بعد ذلك ستحفظ الجلسة تلقائياً

## ⚙️ الإعدادات

| الإعداد | الوصف | القيمة الافتراضية |
|---------|--------|------------------|
| `inactivity_minutes` | عدد الدقائق للاعتبار غير نشط | `5` |
| `auto_reply_message` | رسالة الرد التلقائي | رسالة باللغة العربية |
| `excluded_users` | قائمة معرفات المستخدمين المستثنين | `[]` |
| `enable_logging` | تفعيل السجلات | `true` |
| `rate_limit_seconds` | الحد الأدنى بين الردود لنفس الشخص | `60` |
| `debug_mode` | وضع التطوير (سجلات مفصلة) | `false` |

## 🐳 إدارة Docker

### أوامر مفيدة
```bash
# مراقبة حالة البوت
docker-compose ps

# عرض السجلات المباشرة
docker-compose logs -f telegram-userbot

# إعادة تشغيل البوت
docker-compose restart telegram-userbot

# تحديث البوت
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# الدخول إلى حاوية البوت
docker-compose exec telegram-userbot bash

# نسخ احتياطية للجلسة
docker cp telegram_userbot:/app/userbot_session.session ./backup/
```

### حفظ الجلسة
- الجلسة محفوظة في `./userbot_session.session`
- السجلات محفوظة في `./userbot.log`
- البيانات محفوظة في مجلد `./data/`

## 🔧 كيف يعمل البوت

1. **مراقبة النشاط**: يتتبع آخر رسالة أرسلتها
2. **فحص عدم النشاط**: إذا لم ترسل رسالة لمدة X دقائق
3. **رد تلقائي**: يرد على الرسائل الخاصة الجديدة
4. **منع التكرار**: لا يرد لنفس الشخص مرة أخرى حتى تصبح نشطاً
5. **العودة للنشاط**: عند إرسال أي رسالة، يتوقف الرد التلقائي

## 🛡️ الأمان والخصوصية

- ✅ يعمل على حسابك الشخصي فقط
- ✅ لا يرسل بياناتك لأي خادم خارجي
- ✅ ملف الجلسة محفوظ محلياً
- ✅ لا يحفظ محتوى الرسائل
- ✅ يسجل فقط معلومات أساسية (اختياري)

## 📋 استكشاف الأخطاء

### البوت لا يرد:
- تأكد من أن `inactivity_minutes` مناسب
- تحقق من أن المستخدم ليس في `excluded_users`
- راجع ملف `userbot.log` للأخطاء

### خطأ في الاتصال:
- تحقق من صحة `api_id` و `api_hash`
- تأكد من اتصال الإنترنت
- احذف ملف الجلسة وأعد المحاولة

### البوت يرد كثيراً:
- زد قيمة `rate_limit_seconds`
- قلل قيمة `inactivity_minutes`
- أضف مستخدمين إلى `excluded_users`

## 📝 السجلات

البوت ينشئ ملف `userbot.log` يحتوي على:
- أوقات بدء وإيقاف البوت
- الردود المرسلة (بدون محتوى الرسائل)
- الأخطاء والتحذيرات

## ⚠️ تحذيرات مهمة

- هذا userbot وليس bot عادي (يعمل على حسابك الشخصي)
- للاستخدام الشخصي فقط
- تأكد من الامتثال لشروط خدمة تيليجرام
- اختبر البوت جيداً قبل الاستخدام المستمر
- احتفظ بنسخة احتياطية من ملف الإعدادات

## 🆘 الدعم

إذا واجهت أي مشاكل:
1. راجع ملف `userbot.log`
2. تحقق من الإعدادات في `config.json`
3. تأكد من تحديث المكتبات: `pip install -r requirements.txt --upgrade`

## 📄 الترخيص

هذا المشروع للاستخدام الشخصي فقط. استخدمه بمسؤوليتك الخاصة.
