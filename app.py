#!/usr/bin/env python3
"""
Hugging Face Spaces wrapper for Telegram Userbot
This file is required for Hugging Face Spaces compatibility
"""

import os
import subprocess
import sys
import time
import threading
from pathlib import Path

def setup_environment():
    """Setup environment variables from Hugging Face Spaces secrets"""
    # Get secrets from environment variables
    api_id = os.getenv('API_ID')
    api_hash = os.getenv('API_HASH') 
    phone_number = os.getenv('PHONE_NUMBER')
    
    if api_id and api_hash and phone_number:
        # Update config.json with actual values
        import json
        
        config_path = Path('config.json')
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Update with environment variables
            config['api_id'] = api_id
            config['api_hash'] = api_hash
            config['phone_number'] = phone_number
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            print("✅ Configuration updated from environment variables")
        else:
            print("❌ config.json not found")
    else:
        print("⚠️ Environment variables not set. Please configure API_ID, API_HASH, and PHONE_NUMBER in Spaces secrets.")

def run_userbot():
    """Run the userbot in a separate thread"""
    try:
        print("🚀 Starting Telegram Userbot...")
        subprocess.run([sys.executable, 'main.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running userbot: {e}")
    except KeyboardInterrupt:
        print("🛑 Userbot stopped by user")

def create_simple_interface():
    """Create a simple web interface for monitoring"""
    try:
        import gradio as gr
        
        def get_status():
            """Get userbot status"""
            log_file = Path('userbot.log')
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    return ''.join(lines[-20:])  # Last 20 lines
            return "No logs available yet..."
        
        def get_config():
            """Get current configuration (without sensitive data)"""
            config_file = Path('config.json')
            if config_file.exists():
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # Hide sensitive information
                safe_config = config.copy()
                safe_config['api_hash'] = '***hidden***'
                safe_config['phone_number'] = '***hidden***'
                
                return json.dumps(safe_config, ensure_ascii=False, indent=2)
            return "Configuration not found"
        
        # Create Gradio interface
        with gr.Blocks(title="Telegram Userbot Monitor") as interface:
            gr.Markdown("# 🤖 Telegram Userbot Monitor")
            gr.Markdown("Monitor your Telegram userbot status and logs")
            
            with gr.Tab("Status"):
                status_output = gr.Textbox(
                    label="Recent Logs",
                    lines=15,
                    max_lines=20,
                    value=get_status()
                )
                refresh_btn = gr.Button("🔄 Refresh Logs")
                refresh_btn.click(get_status, outputs=status_output)
            
            with gr.Tab("Configuration"):
                config_output = gr.Textbox(
                    label="Current Configuration",
                    lines=10,
                    value=get_config()
                )
                refresh_config_btn = gr.Button("🔄 Refresh Config")
                refresh_config_btn.click(get_config, outputs=config_output)
            
            with gr.Tab("Help"):
                gr.Markdown("""
                ## 📖 How to use:
                
                1. **First time setup**: The bot will ask for verification code in the logs
                2. **Monitor logs**: Check the Status tab for recent activity
                3. **Configuration**: View current settings in Configuration tab
                
                ## 🔧 Environment Variables needed:
                - `API_ID`: Your Telegram API ID
                - `API_HASH`: Your Telegram API Hash
                - `PHONE_NUMBER`: Your phone number
                
                ## 📝 Features:
                - Auto-reply when inactive for specified minutes
                - Session persistence across restarts
                - Configurable reply message
                - User exclusion list
                """)
        
        return interface
        
    except ImportError:
        print("⚠️ Gradio not available, running in headless mode")
        return None

def main():
    """Main function for Hugging Face Spaces"""
    print("🌟 Telegram Userbot for Hugging Face Spaces")
    print("=" * 50)
    
    # Setup environment
    setup_environment()
    
    # Start userbot in background thread
    userbot_thread = threading.Thread(target=run_userbot, daemon=True)
    userbot_thread.start()
    
    # Create and launch interface
    interface = create_simple_interface()
    
    if interface:
        print("🌐 Starting web interface...")
        interface.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            show_error=True
        )
    else:
        print("📱 Running in headless mode...")
        # Keep the main thread alive
        try:
            while True:
                time.sleep(60)
                print("💓 Userbot is running...")
        except KeyboardInterrupt:
            print("🛑 Shutting down...")

if __name__ == "__main__":
    main()
